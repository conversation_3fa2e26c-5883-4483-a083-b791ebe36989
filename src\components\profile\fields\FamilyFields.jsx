import { useState } from 'react';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trash2, Users } from 'lucide-react';
import { formatDate } from '../utils/dateUtils';

export function FamilyFields({ form }) {
	const [maritalStatus, setMaritalStatus] = useState(
		form.watch('maritalStatus') || ''
	);
	const children = form.watch('children') || [];

	// Watch for marital status changes
	const watchedMaritalStatus = form.watch('maritalStatus');

	// Clear spouse fields when marital status changes to single
	const handleMaritalStatusChange = (value) => {
		setMaritalStatus(value);
		form.setValue('maritalStatus', value);

		if (value === 'single') {
			form.setValue('spouseName', '');
			form.setValue('spouseEmploymentStatus', '');
		}
	};

	// Remove child from the list
	const removeChild = (index) => {
		const currentChildren = form.getValues('children') || [];
		const updatedChildren = currentChildren.filter((_, i) => i !== index);
		form.setValue('children', updatedChildren);
	};

	const isSpouseFieldsVisible =
		watchedMaritalStatus && watchedMaritalStatus !== 'single';

	return (
		<>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<FormField
					control={form.control}
					name="maritalStatus"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Marital Status *</FormLabel>
							<Select
								onValueChange={handleMaritalStatusChange}
								value={field.value}
							>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder="Select marital status" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="single">Single</SelectItem>
									<SelectItem value="married">Married</SelectItem>
									<SelectItem value="divorced">Divorced</SelectItem>
									<SelectItem value="widowed">Widowed</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Conditional spouse fields */}
				{isSpouseFieldsVisible && (
					<>
						<FormField
							control={form.control}
							name="spouseName"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										Spouse Name *
										<Badge variant="outline" className="ml-2 text-xs">
											Required when married
										</Badge>
									</FormLabel>
									<FormControl>
										<Input placeholder="Enter spouse's full name" {...field} />
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="spouseEmploymentStatus"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Spouse Employment Status</FormLabel>
									<Select onValueChange={field.onChange} value={field.value}>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select employment status" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											<SelectItem value="employed">Employed</SelectItem>
											<SelectItem value="unemployed">Unemployed</SelectItem>
											<SelectItem value="self-employed">
												Self-Employed
											</SelectItem>
											<SelectItem value="retired">Retired</SelectItem>
											<SelectItem value="student">Student</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>
					</>
				)}
			</div>

			{/* Children Section */}
			{children.length > 0 && (
				<div className="space-y-4">
					<div className="flex items-center gap-2">
						<Users className="h-5 w-5 text-blue-500" />
						<h4 className="font-medium">Children Information</h4>
						<Badge variant="outline">
							{children.length} child{children.length > 1 ? 'ren' : ''}
						</Badge>
					</div>

					<div className="grid gap-4">
						{children.map((child, index) => (
							<Card key={index} className="relative">
								<CardHeader className="pb-3">
									<div className="flex justify-between items-start">
										<CardTitle className="text-base">
											{child.name || `Child ${index + 1}`}
										</CardTitle>
										<Button
											type="button"
											variant="ghost"
											size="sm"
											onClick={() => removeChild(index)}
											className="text-red-500 hover:text-red-700 hover:bg-red-50"
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</div>
								</CardHeader>
								<CardContent className="pt-0">
									<div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
										<div>
											<span className="font-medium">DOB:</span>{' '}
											{formatDate(child.dob)}
										</div>
										<div>
											<span className="font-medium">Age:</span>{' '}
											{child.age || 'N/A'}
										</div>
										<div>
											<span className="font-medium">Gender:</span>{' '}
											{child.gender || 'N/A'}
										</div>
										<div>
											<span className="font-medium">Nationality:</span>{' '}
											{child.nationality || 'N/A'}
										</div>
									</div>
									{child.birthCertificate && (
										<div className="mt-2 text-sm">
											<span className="font-medium">Birth Certificate:</span>{' '}
											{child.birthCertificate}
										</div>
									)}
								</CardContent>
							</Card>
						))}
					</div>
				</div>
			)}

			<FormField
				control={form.control}
				name="reason"
				render={({ field }) => (
					<FormItem>
						<FormLabel>Reason for Change *</FormLabel>
						<FormControl>
							<Textarea
								placeholder="Please provide a reason for these changes..."
								className="min-h-[100px]"
								{...field}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</>
	);
}
