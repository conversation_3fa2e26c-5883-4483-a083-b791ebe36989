import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
	Check,
	ChevronsUpDown,
	Edit,
	Loader2,
	MapPin,
	Save,
	X,
} from 'lucide-react';
import { calculateAge, cn, formatDate, getICFinPrefixes } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsPersonal } from '@/lib/features/employees/updateEmployeeSlice';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import {
	personalInfoSchema,
	identificationContactDetailsSchema,
	addressSchema,
} from '@/lib/schemas/employeeRegistrationSchema';
import { Textarea } from './ui/textarea';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { fetchCountries } from '@/lib/features/location/locationSlice';
import { religions } from '@/data/religions';
import { z } from 'zod';

// Combine all schemas into one
const combinedSchema = z.object({
	...personalInfoSchema.shape,
	...identificationContactDetailsSchema.shape,
	...addressSchema.shape,
});

const EmployeeDetailsPersonalForm = ({ employeeId, personalDetails }) => {
	const [isAddressManuallyEdited, setIsAddressManuallyEdited] = useState(false);
	const { isLoading } = useAppSelector((store) => store.employee);
	const { countries, isLoading: isLoadingCountries } = useAppSelector(
		(store) => store.location
	);

	const dispatch = useAppDispatch();
	const [isEditing, setIsEditing] = useState(false);
	const [originalFormValues, setOriginalFormValues] = useState(null);

	// Extract all needed properties from personalDetails
	const {
		nameOnNRIC,
		gender,
		dob,
		age,
		nationality,
		race,
		religion,
		icFinNumber,
		residentialStatus,
		prStatus,
		countryDialCode,
		mobile,
		issueDate,
		email,
		country,
		postalCode,
		streetName,
		houseNo,
		levelNo,
		unitNo,
		address,
	} = personalDetails;

	const form = useForm({
		resolver: zodResolver(combinedSchema),
		defaultValues: {
			// Personal Info section
			nameOnNRIC,
			gender,
			dob: dob.split('T')[0],
			age,
			nationality,
			race,
			religion,

			// Contact section
			icFinPrefix: icFinNumber.slice(0, 1),
			icFinNumber: icFinNumber.slice(1),
			residentialStatus,
			countryDialCode,
			mobile,
			issueDate: issueDate ? issueDate.split('T')[0] : '',
			email,

			// Address section
			country,
			postalCode,
			streetName,
			houseNo,
			levelNo,
			unitNo,
			address,
		},
	});

	const watchResidentialStatus = form.watch('residentialStatus');

	useEffect(() => {
		// Get the valid prefixes for the current residential status
		const validPrefixes = getICFinPrefixes(watchResidentialStatus);

		// Get the current prefix
		const currentPrefix = form.getValues('icFinPrefix');

		// If the current prefix is not valid for the new residential status, reset it
		if (currentPrefix && !validPrefixes.includes(currentPrefix)) {
			form.setValue('icFinPrefix', '', { shouldDirty: true });
		}
	}, [watchResidentialStatus, form]);

	useEffect(() => {
		dispatch(fetchCountries());
	}, [dispatch]);

	// Watch fields for calculations and dependencies
	const watchDob = form.watch('dob');
	const watchHouseNo = form.watch('houseNo');
	const watchLevelNo = form.watch('levelNo');
	const watchUnitNo = form.watch('unitNo');
	const watchStreetName = form.watch('streetName');
	const watchPostalCode = form.watch('postalCode');

	// Calculate age based on DOB
	const calculatedAge = useMemo(() => calculateAge(watchDob), [watchDob]);

	useEffect(() => {
		if (form.getValues('age') !== calculatedAge) {
			form.setValue('age', calculatedAge, { shouldDirty: true });
		}
	}, [calculatedAge, form]);

	// Auto-generate address
	useEffect(() => {
		if (isAddressManuallyEdited) return;

		const addressParts = [
			watchHouseNo && `BLK ${watchHouseNo}`,
			(watchLevelNo || watchUnitNo) &&
				`# ${watchLevelNo || ''}${watchUnitNo ? `-${watchUnitNo}` : ''}`,
			watchStreetName,
			watchPostalCode,
		].filter(Boolean);

		const fullAddress = addressParts.join(',\n');

		if (fullAddress !== form.getValues('address')) {
			form.setValue('address', fullAddress, { shouldDirty: true });
		}
	}, [
		watchHouseNo,
		watchLevelNo,
		watchUnitNo,
		watchStreetName,
		watchPostalCode,
		isAddressManuallyEdited,
		form,
	]);

	const onSubmit = async (data) => {
		console.log('Form data to submit:', data);

		// Split the data into three parts for the three different API calls
		const personalInfoData = {
			nameOnNRIC: data.nameOnNRIC,
			gender: data.gender,
			dob: new Date(data.dob).toISOString().split('T')[0],
			age: `${data.age}`,
			nationality: data.nationality,
			race: data.race,
			religion: data.religion,
		};

		const personalContactData = {
			icFinNumber: data.icFinPrefix + data.icFinNumber,
			residentialStatus: data.residentialStatus,
			countryDialCode: data.countryDialCode,
			mobile: data.mobile,
			issueDate: data.issueDate ? new Date(data.issueDate).toISOString() : '',
			email: data.email,
		};

		const personalAddressData = {
			country: data.country,
			postalCode: data.postalCode,
			streetName: data.streetName,
			houseNo: data.houseNo,
			levelNo: data.levelNo,
			unitNo: data.unitNo,
			address: data.address,
		};

		// Make all three API calls in parallel
		const results = await Promise.all([
			dispatch(
				updateEmployeeDetailsPersonal({
					employeeId,
					...personalInfoData,
					...personalContactData,
					...personalAddressData,
				})
			),
		]);

		// Check if all updates were successful
		const allSuccessful = results.every((result) =>
			updateEmployeeDetailsPersonal.fulfilled.match(result)
		);

		if (allSuccessful) {
			setIsEditing(false);
		}
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									form.reset(originalFormValues);
								}
								setIsAddressManuallyEdited(false);
								setIsEditing(false);
							}}
							disabled={isLoading}
						>
							{<X className="h-4 w-4 mr-2" size={16} />}Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();
								setOriginalFormValues(currentValues);
								setIsAddressManuallyEdited(false); // Reset address manual edit flag
								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save' : 'Edit'}
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Personal Information Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle>Personal Information</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="grid grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="nameOnNRIC"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Full Name
												</FormLabel>
												<FormControl>
													<Input {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="gender"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Gender
												</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select gender" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="male">Male</SelectItem>
														<SelectItem value="female">Female</SelectItem>
														<SelectItem value="other">Other</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="dob"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Date of Birth
												</FormLabel>
												<FormControl>
													<Input type="date" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="age"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Age
												</FormLabel>
												<FormControl>
													<Input
														type="number"
														{...field}
														onChange={(e) =>
															field.onChange(parseInt(e.target.value, 10))
														}
														disabled
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="nationality"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Nationality
												</FormLabel>
												<Popover>
													<PopoverTrigger asChild>
														<FormControl>
															<Button
																variant="outline"
																role="combobox"
																className={cn(
																	'w-full justify-between',
																	!field.value && 'text-muted-foreground'
																)}
															>
																{field.value
																	? countries.find(
																			(country) => country._id === field.value
																		)?.name
																	: 'Select country'}
																{isLoadingCountries && (
																	<Loader2 className="h-4 w-4 animate-spin" />
																)}
																{!isLoadingCountries && (
																	<ChevronsUpDown className="opacity-50" />
																)}
															</Button>
														</FormControl>
													</PopoverTrigger>
													<PopoverContent className="p-0">
														<Command>
															<CommandInput placeholder="Search country..." />
															<CommandList>
																<CommandEmpty>No country found.</CommandEmpty>
																<CommandGroup>
																	{countries.map((country) => (
																		<CommandItem
																			key={country._id}
																			value={country.name}
																			onSelect={() => {
																				form.setValue(
																					'nationality',
																					country._id,
																					{
																						shouldValidate: true,
																					}
																				);
																			}}
																		>
																			<Check
																				className={cn(
																					'mr-2 h-4 w-4',
																					field.value === country._id
																						? 'opacity-100'
																						: 'opacity-0'
																				)}
																			/>
																			{country.name}
																		</CommandItem>
																	))}
																</CommandGroup>
															</CommandList>
														</Command>
													</PopoverContent>
												</Popover>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="race"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Race
												</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select race" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{/* Placeholder for race options from backend */}
														<SelectItem value="chinese">Chinese</SelectItem>
														<SelectItem value="eurasian">Eurasian</SelectItem>
														<SelectItem value="indian">Indian</SelectItem>
														<SelectItem value="malay">Malay</SelectItem>
														<SelectItem value="prefer-not-to-contribute">
															Prefer not to contribute
														</SelectItem>
													</SelectContent>
												</Select>

												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="religion"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Religion
												</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select religion" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{religions.map((religion) => (
															<SelectItem
																key={religion.value}
																value={religion.value}
															>
																{religion.label}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</Form>
						) : (
							<div>
								<div className="grid grid-cols-2 gap-4">
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Full Name
										</p>
										<p>{nameOnNRIC}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Gender
										</p>
										<p className="capitalize">{gender}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Date of Birth
										</p>
										<p>{formatDate(dob)}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Age
										</p>
										<p>{age}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Nationality
										</p>
										<p className="capitalize">
											{
												countries.find((country) => country._id === nationality)
													?.name
											}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Race
										</p>
										<p className="capitalize">{race}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Religion
										</p>
										<p className="capitalize">{religion}</p>
									</div>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Identification & Contact Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle>Identification & Contact</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="grid grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="residentialStatus"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Residential Status
												</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
													disabled={nationality === 'Singapore'}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select residential status" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{nationality !== 'Singapore' && (
															<>
																<SelectItem value="Singapore PR">
																	Singapore PR
																</SelectItem>
																<SelectItem value="Employment Pass">
																	Employment Pass
																</SelectItem>
																<SelectItem value="SPass">SPass</SelectItem>
																<SelectItem value="Work Permit">
																	Work Permit
																</SelectItem>
																<SelectItem value="LOC">LOC</SelectItem>
															</>
														)}
														<SelectItem value="Singapore Citizen">
															Singapore Citizen
														</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
									<div className="flex gap-2">
										<FormField
											control={form.control}
											name="icFinPrefix"
											render={({ field }) => (
												<FormItem className="w-1/3">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Prefix
													</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Prefix" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{getICFinPrefixes(watchResidentialStatus).map(
																(prefix) => (
																	<SelectItem key={prefix} value={prefix}>
																		{prefix}
																	</SelectItem>
																)
															)}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name="icFinNumber"
											render={({ field }) => (
												<FormItem className="w-3/4">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														IC/FIN Number
													</FormLabel>
													<FormControl>
														<Input
															placeholder="Enter IC/FIN number"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
									<FormField
										control={form.control}
										name="issueDate"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Issue Date
												</FormLabel>
												<FormControl>
													<Input
														type="date"
														{...field}
														disabled={nationality === 'Singapore'}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<div className="flex gap-2">
										<FormField
											control={form.control}
											name="countryDialCode"
											render={({ field }) => (
												<FormItem className="w-1/3">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Code
													</FormLabel>
													<FormControl>
														<Input placeholder="+65" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name="mobile"
											render={({ field }) => (
												<FormItem className="w-2/3">
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Mobile
													</FormLabel>
													<FormControl>
														<Input
															type="tel"
															placeholder="Enter mobile number"
															{...field}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
									<FormField
										control={form.control}
										name="email"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Email
												</FormLabel>
												<FormControl>
													<Input
														type="email"
														placeholder="Enter email"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</Form>
						) : (
							<div>
								<div className="grid grid-cols-2 gap-4">
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Residential Status
										</p>
										<p>{residentialStatus}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											PR Status
										</p>
										<p className="capitalize">
											{prStatus?.replace(/-/g, ' ') || '-'}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											IC/FIN Number
										</p>
										<p>{icFinNumber}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Issue Date
										</p>
										<p>{issueDate ? formatDate(issueDate) : '-'}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Email
										</p>
										<p>{email}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Mobile
										</p>
										<p>
											{countryDialCode} {mobile}
										</p>
									</div>
								</div>
							</div>
						)}
					</CardContent>
				</Card>

				{/* Address Card */}
				<Card className="md:col-span-2">
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle>Residential Address</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
									<FormField
										control={form.control}
										name="country"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Country
												</FormLabel>
												<Popover>
													<PopoverTrigger asChild>
														<FormControl>
															<Button
																variant="outline"
																role="combobox"
																className={cn(
																	'w-full justify-between',
																	!field.value && 'text-muted-foreground'
																)}
															>
																{field.value
																	? countries.find(
																			(country) => country._id === field.value
																		)?.name
																	: 'Select country'}
																{isLoadingCountries && (
																	<Loader2 className="h-4 w-4 animate-spin" />
																)}
																{!isLoadingCountries && (
																	<ChevronsUpDown className="opacity-50" />
																)}
															</Button>
														</FormControl>
													</PopoverTrigger>
													<PopoverContent className="p-0">
														<Command>
															<CommandInput placeholder="Search country..." />
															<CommandList>
																<CommandEmpty>No country found.</CommandEmpty>
																<CommandGroup>
																	{countries.map((country) => (
																		<CommandItem
																			key={country._id}
																			value={country.name}
																			onSelect={() => {
																				form.setValue('country', country._id, {
																					shouldValidate: true,
																				});
																			}}
																		>
																			<Check
																				className={cn(
																					'mr-2 h-4 w-4',
																					field.value === country._id
																						? 'opacity-100'
																						: 'opacity-0'
																				)}
																			/>
																			{country.name}
																		</CommandItem>
																	))}
																</CommandGroup>
															</CommandList>
														</Command>
													</PopoverContent>
												</Popover>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="postalCode"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Postal Code
												</FormLabel>
												<FormControl>
													<Input placeholder="Enter postal code" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="streetName"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Street Name
												</FormLabel>
												<FormControl>
													<Input placeholder="Enter street name" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="houseNo"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Block/House No.
												</FormLabel>
												<FormControl>
													<Input
														placeholder="Enter block/house number"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="levelNo"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Level No.
												</FormLabel>
												<FormControl>
													<Input placeholder="Enter level number" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="unitNo"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Unit No.
												</FormLabel>
												<FormControl>
													<Input placeholder="Enter unit number" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="address"
										render={({ field }) => (
											<FormItem className="col-span-full">
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Full Address
												</FormLabel>
												<FormControl>
													<Textarea
														placeholder="Enter full address"
														className="resize-none"
														{...field}
														onChange={(e) => {
															field.onChange(e);
															setIsAddressManuallyEdited(true);
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</Form>
						) : (
							<div className="flex items-start gap-4">
								<MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
								<div>
									<p className="whitespace-pre-line">{address}</p>
									<p className="text-sm text-muted-foreground mt-2">
										Block {houseNo || '-'}, Level {levelNo || '-'}, Unit{' '}
										{unitNo || '-'}, {streetName || '-'}, {postalCode || '-'}
									</p>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
			</div>
		</>
	);
};

export default EmployeeDetailsPersonalForm;
