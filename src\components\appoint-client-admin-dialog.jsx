'use client';

import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Check, ChevronsUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from '@/components/ui/command';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import {
	appointAdmin,
	fetchEmployeesForOrganization,
} from '@/lib/features/glorified-client-admin/glorifiedClientAdminSlice';
import { LoadingProgressBar } from './loading-component';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';

export function AppointAdminDialog({ open, onOpenChange, company }) {
	const [selectedEmployee, setSelectedEmployee] = useState(null);
	const [comboboxOpen, setComboboxOpen] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [progress, setProgress] = useState(0);
	const dispatch = useAppDispatch();
	const { employees } = useAppSelector((store) => store.glorifiedClientAdmin);

	useEffect(() => {
		if (open && company?._id) {
			dispatch(fetchEmployeesForOrganization(company._id));
		}
	}, [dispatch, company._id, open]);

	const handleAppointAdmin = async () => {
		if (!selectedEmployee) return;

		setIsSubmitting(true);

		// Start progress animation
		const interval = setInterval(() => {
			setProgress((prev) => {
				if (prev >= 90) {
					clearInterval(interval);
					return 90;
				}
				return prev + 10;
			});
		}, 300);

		try {
			await dispatch(
				appointAdmin({
					employeeId: selectedEmployee._id,
					companyId: company._id,
				})
			).unwrap();

			// Complete progress
			setProgress(100);

			// Close dialog after a short delay
			setTimeout(() => {
				onOpenChange(false);
				setSelectedEmployee(null);
				setProgress(0);
			}, 500);
		} catch (error) {
			console.error('Failed to appoint admin:', error);
		} finally {
			clearInterval(interval);
			setIsSubmitting(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Appoint Client Admin</DialogTitle>
					<DialogDescription>
						Select an employee to appoint as client admin for{' '}
						{company?.businessName}
					</DialogDescription>
				</DialogHeader>

				{isSubmitting && (
					<div className="my-4">
						<LoadingProgressBar progress={progress} />
						<p className="text-center text-sm mt-2 font-medium text-amber-600">
							⚠️ Do not reload or refresh — the client admin is being updated.
						</p>
					</div>
				)}

				<div className="grid gap-4 py-4">
					<div className="space-y-2">
						<label className="text-sm font-medium">Select Employee</label>
						<Popover open={comboboxOpen} onOpenChange={setComboboxOpen}>
							<PopoverTrigger asChild>
								<Button
									variant="outline"
									role="combobox"
									aria-expanded={comboboxOpen}
									className="w-full justify-between"
									disabled={isSubmitting}
								>
									{selectedEmployee
										? selectedEmployee.personalDetails?.nameOnNRIC
										: 'Select employee...'}
									<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
								</Button>
							</PopoverTrigger>
							<PopoverContent className="w-[--radix-popover-trigger-width] p-0">
								<Command>
									<CommandInput placeholder="Search employee..." />
									<CommandList>
										<CommandEmpty>No employee found.</CommandEmpty>
										<CommandGroup>
											{employees.map((employee) => (
												<CommandItem
													key={employee._id}
													value={employee.personalDetails.nameOnNRIC}
													onSelect={() => {
														setSelectedEmployee(employee);
														setComboboxOpen(false);
													}}
												>
													<Check
														className={cn(
															'mr-2 h-4 w-4',
															selectedEmployee?._id === employee._id
																? 'opacity-100'
																: 'opacity-0'
														)}
													/>
													{employee?.personalDetails?.nameOnNRIC}
												</CommandItem>
											))}
										</CommandGroup>
									</CommandList>
								</Command>
							</PopoverContent>
						</Popover>
					</div>
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						onClick={handleAppointAdmin}
						disabled={!selectedEmployee || isSubmitting}
					>
						{isSubmitting ? 'Appointing...' : 'Appoint Admin'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
