import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Briefcase, Pencil } from 'lucide-react';
import { formatDate } from '../utils/dateUtils';

export function ExperienceTab({ experience, onEditSection }) {
	return (
		<div className="grid grid-cols-1 gap-6">
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<Briefcase className="h-5 w-5 text-orange-600" />
								Work Experience
								{experience && experience.length > 0 && (
									<Badge variant="outline">
										{experience.length} position
										{experience.length > 1 ? 's' : ''}
									</Badge>
								)}
							</CardTitle>
							<CardDescription>
								Your work experience. Click edit to request changes.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('experience')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					{experience && experience.length > 0 ? (
						<div className="space-y-4">
							{experience.map((exp, index) => (
								<div key={index} className="p-4 border rounded-lg bg-slate-50">
									<div className="flex justify-between items-start mb-2">
										<div>
											<h4 className="font-medium">{exp.designation}</h4>
											<p className="text-sm text-muted-foreground">
												{exp.company}
											</p>
										</div>
										<Badge variant="outline">
											{exp.startDate ? formatDate(exp.startDate) : 'N/A'} -{' '}
											{exp.endDate ? formatDate(exp.endDate) : 'Present'}
										</Badge>
									</div>
									{exp.description && (
										<div className="mt-2">
											<p className="text-sm">{exp.description}</p>
										</div>
									)}
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<Briefcase className="h-12 w-12 mx-auto mb-4 opacity-50" />
							<p>No work experience information available</p>
							<p className="text-sm">
								Click &quot;Edit Section&quot; to add your work history
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
