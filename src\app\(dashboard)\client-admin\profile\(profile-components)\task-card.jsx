import React from 'react';
import {
	PlaneIcon,
	FileTextIcon,
	HourglassIcon,
	LockIcon,
	CheckCircle,
	ClockIcon,
	RefreshCcwIcon,
	XCircleIcon,
	CheckCircleIcon,
} from 'lucide-react';
import * as Icons from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const LeaveCard = ({
	tasks = [
		{
			id: 1,
			name: 'Fix login issue in user auth flow',
			description:
				'Resolve the bug where users are unable to log in after recent updates to the authentication system.',
			status: 'pending',
			time: '5 hours ago',
			iconName: 'PlaneIcon',
			iconBackgroundColor: '#F9C5CD',
		},
		{
			id: 2,
			name: 'Update API documentation',
			description:
				'Revise the outdated API documentation to include the latest changes in endpoints and methods.',
			status: 'completed',
			time: '2 days ago',
			iconName: 'FileTextIcon',
			iconBackgroundColor: '#AFC7F9',
		},
		{
			id: 3,
			name: 'Optimize database queries',
			description:
				'Improve the performance of database queries for fetching user data to reduce load time during peak usage.',
			status: 'in progress',
			time: '1 day ago',
			iconName: 'HourglassIcon',
			iconBackgroundColor: '#91F6A8',
		},
		{
			id: 4,
			name: 'Implement new user onboarding flow',
			description:
				'Design and implement a new onboarding process for users to enhance their initial experience with the application.',
			status: 'locked',
			time: '3 days ago',
			iconName: 'LockIcon',
			iconBackgroundColor: '#90ADE7',
		},
	],
}) => {
	return (
		<div className="h-full shadow-sm rounded-xl p-3 space-y-3 text-sm">
			<div className="flex flex-col gap-2">
				<h2 className="text-base font-semibold text-card-foreground flex items-center gap-2 h-8">
					Tasks
				</h2>
			</div>

			<div className="max-h-[360px] overflow-y-auto space-y-2 border-t pt-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
				{tasks.map((task) => {
					const Icon = Icons[task.iconName];

					return (
						<div key={task.id} className="flex gap-2">
							<div
								class="inline-flex items-center justify-center rounded-md text-sm font-medium shadow p-2 h-8 w-8"
								style={{ backgroundColor: task.iconBackgroundColor }}
							>
								{Icon && <Icon className="h-5 w-5 text-black" />}
							</div>
							<div>
								<div className="font-semibold text-card-foreground">
									{task.name}
								</div>
								<div>{task.description}</div>
								<div className="text-xs pt-1 text-card-foreground opacity-80">
									{task.time}
								</div>
							</div>
						</div>
					);
				})}
			</div>
		</div>
	);
};

export default LeaveCard;
