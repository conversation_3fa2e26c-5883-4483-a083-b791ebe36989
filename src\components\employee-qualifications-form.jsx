'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { PlusCircle, X, Trash2, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { educationAndSkillsSchema } from '@/lib/schemas/employeeRegistrationSchema';
import { Textarea } from '@/components/ui/textarea';
import { updateEmployeeQualificationsDetails } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { toast } from 'sonner';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import { qualificationTypes } from '@/lib/utils';
import { LoadingSubmitButton } from './loading-component';

const combinedSchema = z.object({
	...educationAndSkillsSchema.shape,
	experienceDetails: z
		.array(
			z.object({
				location: z.string().nonempty('Location is required'),
				companyName: z.string().nonempty('Company Name is required'),
				designation: z.string().nonempty('Designation is required'),
				periodFrom: z
					.string()
					.regex(
						/^\d{4}-\d{2}-\d{2}$/,
						'Start Date must be in YYYY-MM-DD format'
					),
				periodTo: z
					.string()
					.regex(
						/^\d{4}-\d{2}-\d{2}$/,
						'End Date must be in YYYY-MM-DD format'
					),
				reasonForLeaving: z.string().optional(),
			})
		)
		.max(5, 'Maximum 5 experience details are accepted')
		.optional(),
});

export function EmployeeEducationSkillsForm() {
	const [hardSkillInput, setHardSkillInput] = useState('');
	const [softSkillInput, setSoftSkillInput] = useState('');
	const [uploadedFiles, setUploadedFiles] = useState({});
	const dispatch = useAppDispatch();
	const { employeeDetails, isLoading } = useAppSelector(
		(store) => store.employee
	);

	const form = useForm({
		resolver: zodResolver(combinedSchema),
		defaultValues: {
			// employeeOrgId: '',
			educationalDetails: [
				{
					instituteName: '',
					qualification: '',
					grade: '',
					startDate: '',
					endDate: '',
				},
			],
			hardSkills: [],
			softSkills: [],
			experienceDetails: [],
		},
	});

	const {
		fields: educationFields,
		append: appendEducation,
		remove: removeEducation,
	} = useFieldArray({
		name: 'educationalDetails',
		control: form.control,
	});

	const {
		fields: experienceFields,
		append: appendExperience,
		remove: removeExperience,
	} = useFieldArray({
		name: 'experienceDetails',
		control: form.control,
	});

	const handleFileChange = (index, e) => {
		const file = e.target.files?.[0] || null;
		if (file) {
			setUploadedFiles((prev) => ({ ...prev, [index]: file }));
		}
	};

	function onSubmit(data) {
		const formData = new FormData();

		if (employeeDetails?.employeeId || employeeDetails?._id) {
			const employeeId = employeeDetails?.employeeId || employeeDetails?._id;
			formData.append('employeeId', employeeId);
		} else {
			toast.error('Employee ID not found, try editing the employee details.');
			return;
		}

		// Append normal fields
		Object.entries(data).forEach(([key, value]) => {
			if (Array.isArray(value)) {
				value.forEach((item, index) => {
					if (typeof item === 'object') {
						Object.entries(item).forEach(([subKey, subValue]) => {
							formData.append(`${key}[${index}][${subKey}]`, subValue);
						});
					} else {
						formData.append(`${key}[${index}]`, item);
					}
				});
			} else {
				formData.append(key, value);
			}
		});

		// Append files separately
		Object.values(uploadedFiles).forEach((file) => {
			if (file) {
				formData.append('documents', file); // Ensure consistent field name
			}
		});

		dispatch(updateEmployeeQualificationsDetails(formData));
	}

	/* Use this to debug form entries

	// useEffect(() => {
	// 	console.log(form.formState.errors);
	// }, [form.formState.errors]);

	// useEffect(() => {
	// 	const subscription = form.watch((data) => {
	// 		console.log(data);
	// 	});
	// 	return () => subscription.unsubscribe();
	// }, [form]);
	
	*/
	useEffect(() => {
		console.log(form.formState.errors);
	}, [form.formState.errors]);

	useEffect(() => {
		const subscription = form.watch((data) => {
			console.log(data);
		});
		return () => subscription.unsubscribe();
	}, [form]);

	const addSkill = (type, field) => {
		if (type === 'hardSkill') {
			if (hardSkillInput.length >= 2) {
				const currentSkills = field.value ?? [];
				const newSkills = [...currentSkills, hardSkillInput];
				field.onChange(newSkills);
				setHardSkillInput('');
			}
		}
		if (type === 'softSkill') {
			if (softSkillInput.length >= 2) {
				const currentSkills = field.value ?? [];
				const newSkills = [...currentSkills, softSkillInput];
				field.onChange(newSkills);
				setSoftSkillInput('');
			}
		}
	};

	const removeSkill = (field, index) => {
		const currentSkills = field.value ?? [];
		const newSkills = currentSkills.filter((_, i) => i !== index);
		field.onChange(newSkills);
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				{/* Employee ID field */}
				{/* <FormField
					control={form.control}
					name="employeeOrgId"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Employee ID</FormLabel>
							<FormControl>
								<Input placeholder="Enter employee ID" {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/> */}

				{/* Educational Details section */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Educational Details</h3>
					<Separator />
					{educationFields.map((field, index) => (
						<Card key={field.id} className="relative">
							<CardContent className="pt-6">
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
									<FormField
										control={form.control}
										name={`educationalDetails.${index}.qualification`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Qualification</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select qualification" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem
															value={qualificationTypes.UNDER_GRADUATE}
														>
															Under Graduate
														</SelectItem>
														<SelectItem
															value={qualificationTypes.POST_GRADUATE}
														>
															Post Graduate
														</SelectItem>
														<SelectItem
															value={qualificationTypes.NO_FORMAL_EDUCATION}
														>
															No Formal Education
														</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
									{form.watch(`educationalDetails.${index}.qualification`) !==
										qualificationTypes.NO_FORMAL_EDUCATION && (
										<>
											<FormField
												control={form.control}
												name={`educationalDetails.${index}.instituteName`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Institute Name</FormLabel>
														<FormControl>
															<Input
																placeholder="Enter institute name"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`educationalDetails.${index}.grade`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Grade</FormLabel>
														<FormControl>
															<Input placeholder="Enter grade" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`educationalDetails.${index}.startDate`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Start Date</FormLabel>
														<FormControl>
															<Input type="date" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`educationalDetails.${index}.endDate`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>End Date</FormLabel>
														<FormControl>
															<Input type="date" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`educationalDetails.${index}.document`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Document</FormLabel>
														<FormControl>
															<Input
																type="file"
																accept="image/*,.pdf"
																multiple={false}
																onChange={(e) => {
																	handleFileChange(index, e);
																	field.onChange(e.target.files?.[0] || null);
																}}
															/>
														</FormControl>
														<FormDescription>
															Upload image or PDF document, Make sure the file
															name matches your qualification.
														</FormDescription>
														<FormMessage />
													</FormItem>
												)}
											/>
										</>
									)}
								</div>

								<Button
									type="button"
									variant="ghost"
									size="icon"
									onClick={() => removeEducation(index)}
									className="absolute top-2 right-2"
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							</CardContent>
						</Card>
					))}
					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={() =>
							appendEducation({
								// id: crypto.randomUUID(),
								instituteName: '',
								qualification: '',
								grade: '',
								startDate: '',
								endDate: '',
							})
						}
					>
						<PlusCircle className="mr-2 h-4 w-4" />
						Add Education
					</Button>
				</div>

				{/* Skills section */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Skills</h3>
					<Separator />
					<div className="grid md:grid-cols-2 gap-4">
						<FormField
							control={form.control}
							name="hardSkills"
							render={({ field }) => (
								<FormItem>
									<div className="flex space-x-2 mb-4">
										<Input
											placeholder="Enter hard skills"
											value={hardSkillInput}
											onChange={(e) => setHardSkillInput(e.target.value)}
											onKeyPress={(e) => {
												if (e.key === 'Enter') {
													e.preventDefault();
													addSkill('hardSkill', field);
												}
											}}
										/>
										{hardSkillInput.length >= 2 && (
											<Button
												type="button"
												onClick={() => addSkill('hardSkill', field)}
											>
												Add Skill
											</Button>
										)}
									</div>
									<div className="flex flex-wrap gap-2">
										{field.value?.map((skill, index) => (
											<Badge key={index} variant="secondary">
												{skill}
												<Button
													type="button"
													variant="ghost"
													size="sm"
													className="ml-2 h-4 w-4 p-0"
													onClick={() => removeSkill(field, index)}
												>
													<X className="h-3 w-3" />
												</Button>
											</Badge>
										))}
									</div>
									<FormMessage>
										{form.formState.errors.hardSkills?.message}
									</FormMessage>
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="softSkills"
							render={({ field }) => (
								<FormItem>
									<div className="flex space-x-2 mb-4">
										<Input
											placeholder="Enter soft skills"
											value={softSkillInput}
											onChange={(e) => setSoftSkillInput(e.target.value)}
											onKeyPress={(e) => {
												if (e.key === 'Enter') {
													e.preventDefault();
													addSkill('softSkill', field);
												}
											}}
										/>
										{softSkillInput.length >= 2 && (
											<Button
												type="button"
												onClick={() => addSkill('softSkill', field)}
											>
												Add Skill
											</Button>
										)}
									</div>
									<div className="flex flex-wrap gap-2">
										{field.value?.map((skill, index) => (
											<Badge key={index} variant="secondary">
												{skill}
												<Button
													type="button"
													variant="ghost"
													size="sm"
													className="ml-2 h-4 w-4 p-0"
													onClick={() => removeSkill(field, index)}
												>
													<X className="h-3 w-3" />
												</Button>
											</Badge>
										))}
									</div>
									<FormMessage>
										{form.formState.errors.softSkills?.message}
									</FormMessage>
								</FormItem>
							)}
						/>
					</div>
				</div>

				{/* Experience Details section */}
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Experience Details</h3>
					<Separator />
					{experienceFields.length > 0 ? (
						<>
							{experienceFields.map((field, index) => (
								<Card key={field.id} className="relative">
									<CardContent className="pt-6">
										<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
											<FormField
												control={form.control}
												name={`experienceDetails.${index}.location`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Location</FormLabel>
														<FormControl>
															<Input placeholder="Enter location" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`experienceDetails.${index}.companyName`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Company Name</FormLabel>
														<FormControl>
															<Input
																placeholder="Enter company name"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`experienceDetails.${index}.designation`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>Designation</FormLabel>
														<FormControl>
															<Input
																placeholder="Enter designation"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`experienceDetails.${index}.periodFrom`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>From</FormLabel>
														<FormControl>
															<Input type="date" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`experienceDetails.${index}.periodTo`}
												render={({ field }) => (
													<FormItem>
														<FormLabel>To</FormLabel>
														<FormControl>
															<Input type="date" {...field} />
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name={`experienceDetails.${index}.reasonForLeaving`}
												render={({ field }) => (
													<FormItem className="col-span-1 md:col-span-2 lg:col-span-3">
														<FormLabel>Reason for Leaving</FormLabel>
														<FormControl>
															<Textarea
																placeholder="Enter reason for leaving"
																{...field}
																className="resize-none"
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
										<Button
											type="button"
											variant="ghost"
											size="icon"
											onClick={() => removeExperience(index)}
											className="absolute top-2 right-2"
										>
											<Trash2 className="h-4 w-4" />
										</Button>
									</CardContent>
								</Card>
							))}
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={() =>
									appendExperience({
										location: '',
										companyName: '',
										designation: '',
										periodFrom: '',
										periodTo: '',
										reasonForLeaving: '',
									})
								}
							>
								<PlusCircle className="mr-2 h-4 w-4" />
								Add Experience
							</Button>
						</>
					) : (
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={() =>
								appendExperience({
									location: '',
									companyName: '',
									designation: '',
									periodFrom: '',
									periodTo: '',
									reasonForLeaving: '',
								})
							}
						>
							<PlusCircle className="mr-2 h-4 w-4" />
							Add Experience
						</Button>
					)}
				</div>

				<div className="flex justify-end space-x-4">
					<LoadingSubmitButton
						isLoading={isLoading}
						buttonText={'Save and Next'}
						buttonLoadingText={'Saving...'}
					/>
				</div>
			</form>
		</Form>
	);
}
