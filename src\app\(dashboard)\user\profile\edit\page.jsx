'use client';
import { ProfilePageComponent } from '@/components/profile-page-component';
import { ProfilePageMain } from '@/components/profile/ProfilePageMain';
import { fetchProfilePageDetails } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { useEffect } from 'react';
export default function ProfilePage() {
	const dispatch = useAppDispatch();
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	useEffect(() => {
		dispatch(fetchProfilePageDetails(authenticatedUser?.userId));
	}, [dispatch, authenticatedUser]);

	return (
		<div className="container mx-auto">
			<ProfilePageComponent />
      {/* <ProfilePageMain /> */}
		</div>
	);
}
