import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, Save, X } from 'lucide-react';

export function MultipleEntriesManager({ section, entries, onEntriesChange }) {
	const [editingIndex, setEditingIndex] = useState(null);
	const [showAddForm, setShowAddForm] = useState(false);
	const [formData, setFormData] = useState(entry);

	// Add new entry
	const addEntry = (newEntry) => {
		onEntriesChange([...entries, newEntry]);
		setShowAddForm(false);
	};

	// Update existing entry
	const updateEntry = (index, updatedEntry) => {
		const newEntries = [...entries];
		newEntries[index] = updatedEntry;
		onEntriesChange(newEntries);
		setEditingIndex(null);
	};

	// Remove entry
	const removeEntry = (index) => {
		const newEntries = entries.filter((_, i) => i !== index);
		onEntriesChange(newEntries);
		setEditingIndex(null);
	};

	// Render entry form based on section
	const renderEntryForm = (entry = {}, index = null, isNew = false) => {
		// const [formData, setFormData] = useState(entry);

		const handleSave = () => {
			if (isNew) {
				addEntry(formData);
			} else {
				updateEntry(index, formData);
			}
		};

		const handleCancel = () => {
			if (isNew) {
				setShowAddForm(false);
			} else {
				setEditingIndex(null);
			}
		};

		switch (section) {
			case 'education':
				return (
					<div className="space-y-4 p-4 border rounded-md bg-slate-50">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{isNew ? 'Add New Education' : 'Edit Education'}
							</h5>
							<div className="flex gap-2">
								<Button type="button" size="sm" onClick={handleCancel}>
									<X className="h-4 w-4 mr-1" />
									Cancel
								</Button>
								<Button type="button" size="sm" onClick={handleSave}>
									<Save className="h-4 w-4 mr-1" />
									Save
								</Button>
								{!isNew && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										<Trash2 className="h-4 w-4 mr-1" />
										Remove
									</Button>
								)}
							</div>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="text-sm font-medium">Institution *</label>
								<Input
									value={formData.institution || ''}
									onChange={(e) =>
										setFormData({ ...formData, institution: e.target.value })
									}
									placeholder="Enter institution name"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">
									Degree/Qualification *
								</label>
								<Input
									value={formData.degree || ''}
									onChange={(e) =>
										setFormData({ ...formData, degree: e.target.value })
									}
									placeholder="Enter degree or qualification"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Field of Study</label>
								<Input
									value={formData.fieldOfStudy || ''}
									onChange={(e) =>
										setFormData({ ...formData, fieldOfStudy: e.target.value })
									}
									placeholder="Enter field of study"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">
									Year of Completion
								</label>
								<Input
									type="number"
									value={formData.yearOfCompletion || ''}
									onChange={(e) =>
										setFormData({
											...formData,
											yearOfCompletion: e.target.value,
										})
									}
									placeholder="Enter year"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Grade/CGPA</label>
								<Input
									value={formData.grade || ''}
									onChange={(e) =>
										setFormData({ ...formData, grade: e.target.value })
									}
									placeholder="Enter grade or CGPA"
								/>
							</div>
						</div>
					</div>
				);

			case 'experience':
				return (
					<div className="space-y-4 p-4 border rounded-md bg-slate-50">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{isNew ? 'Add New Experience' : 'Edit Experience'}
							</h5>
							<div className="flex gap-2">
								<Button type="button" size="sm" onClick={handleCancel}>
									<X className="h-4 w-4 mr-1" />
									Cancel
								</Button>
								<Button type="button" size="sm" onClick={handleSave}>
									<Save className="h-4 w-4 mr-1" />
									Save
								</Button>
								{!isNew && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										<Trash2 className="h-4 w-4 mr-1" />
										Remove
									</Button>
								)}
							</div>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="text-sm font-medium">Company *</label>
								<Input
									value={formData.company || ''}
									onChange={(e) =>
										setFormData({ ...formData, company: e.target.value })
									}
									placeholder="Enter company name"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Designation *</label>
								<Input
									value={formData.designation || ''}
									onChange={(e) =>
										setFormData({ ...formData, designation: e.target.value })
									}
									placeholder="Enter job title"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Start Date</label>
								<Input
									type="date"
									value={formData.startDate || ''}
									onChange={(e) =>
										setFormData({ ...formData, startDate: e.target.value })
									}
								/>
							</div>
							<div>
								<label className="text-sm font-medium">End Date</label>
								<Input
									type="date"
									value={formData.endDate || ''}
									onChange={(e) =>
										setFormData({ ...formData, endDate: e.target.value })
									}
								/>
							</div>
							<div className="md:col-span-2">
								<label className="text-sm font-medium">Description</label>
								<Textarea
									value={formData.description || ''}
									onChange={(e) =>
										setFormData({ ...formData, description: e.target.value })
									}
									placeholder="Describe your role and responsibilities"
									rows={3}
								/>
							</div>
						</div>
					</div>
				);

			case 'contact':
				return (
					<div className="space-y-4 p-4 border rounded-md bg-slate-50">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{isNew ? 'Add New Contact' : 'Edit Contact'}
							</h5>
							<div className="flex gap-2">
								<Button type="button" size="sm" onClick={handleCancel}>
									<X className="h-4 w-4 mr-1" />
									Cancel
								</Button>
								<Button type="button" size="sm" onClick={handleSave}>
									<Save className="h-4 w-4 mr-1" />
									Save
								</Button>
								{!isNew && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										<Trash2 className="h-4 w-4 mr-1" />
										Remove
									</Button>
								)}
							</div>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label className="text-sm font-medium">Contact Type *</label>
								<Select
									value={formData.type || ''}
									onValueChange={(value) =>
										setFormData({ ...formData, type: value })
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select contact type" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="emergency">Emergency Contact</SelectItem>
										<SelectItem value="family">Family Member</SelectItem>
										<SelectItem value="friend">Friend</SelectItem>
										<SelectItem value="colleague">Colleague</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div>
								<label className="text-sm font-medium">Name *</label>
								<Input
									value={formData.name || ''}
									onChange={(e) =>
										setFormData({ ...formData, name: e.target.value })
									}
									placeholder="Enter contact name"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Relationship</label>
								<Input
									value={formData.relationship || ''}
									onChange={(e) =>
										setFormData({ ...formData, relationship: e.target.value })
									}
									placeholder="e.g., Spouse, Parent, Friend"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Phone Number *</label>
								<Input
									value={formData.phone || ''}
									onChange={(e) =>
										setFormData({ ...formData, phone: e.target.value })
									}
									placeholder="Enter phone number"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Email</label>
								<Input
									type="email"
									value={formData.email || ''}
									onChange={(e) =>
										setFormData({ ...formData, email: e.target.value })
									}
									placeholder="Enter email address"
								/>
							</div>
							<div>
								<label className="text-sm font-medium">Address</label>
								<Textarea
									value={formData.address || ''}
									onChange={(e) =>
										setFormData({ ...formData, address: e.target.value })
									}
									placeholder="Enter address"
									rows={2}
								/>
							</div>
						</div>
					</div>
				);

			default:
				return <div>Unknown section</div>;
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex justify-between items-center">
				<h4 className="font-medium capitalize">{section} Information</h4>
				<Button
					type="button"
					size="sm"
					onClick={() => setShowAddForm(true)}
					disabled={showAddForm}
				>
					<Plus className="h-4 w-4 mr-1" />
					Add {section}
				</Button>
			</div>

			{/* Existing entries */}
			{entries.map((entry, index) => (
				<Card key={index}>
					{editingIndex === index ? (
						renderEntryForm(entry, index, false)
					) : (
						<CardContent className="pt-4">
							<div className="flex justify-between items-start">
								<div className="flex-1">
									{section === 'education' && (
										<div>
											<h5 className="font-medium">
												{entry.degree} - {entry.institution}
											</h5>
											<p className="text-sm text-muted-foreground">
												{entry.fieldOfStudy} • {entry.yearOfCompletion} • Grade:{' '}
												{entry.grade || 'N/A'}
											</p>
										</div>
									)}
									{section === 'experience' && (
										<div>
											<h5 className="font-medium">
												{entry.designation} at {entry.company}
											</h5>
											<p className="text-sm text-muted-foreground">
												{entry.startDate} - {entry.endDate || 'Present'}
											</p>
											{entry.description && (
												<p className="text-sm mt-1">{entry.description}</p>
											)}
										</div>
									)}
									{section === 'contact' && (
										<div>
											<h5 className="font-medium">{entry.name}</h5>
											<p className="text-sm text-muted-foreground">
												{entry.relationship} • {entry.phone} • {entry.email}
											</p>
											<Badge variant="outline" className="mt-1">
												{entry.type}
											</Badge>
										</div>
									)}
								</div>
								<Button
									type="button"
									variant="ghost"
									size="sm"
									onClick={() => setEditingIndex(index)}
								>
									<Edit className="h-4 w-4" />
								</Button>
							</div>
						</CardContent>
					)}
				</Card>
			))}

			{/* Add new entry form */}
			{showAddForm && renderEntryForm({}, null, true)}
		</div>
	);
}
