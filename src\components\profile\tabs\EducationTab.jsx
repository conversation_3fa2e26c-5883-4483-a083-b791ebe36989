import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { GraduationCap, Pencil } from 'lucide-react';

export function EducationTab({ education, onEditSection }) {
	return (
		<div className="grid grid-cols-1 gap-6">
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<GraduationCap className="h-5 w-5 text-purple-600" />
								Education Information
								{education && education.length > 0 && (
									<Badge variant="outline">
										{education.length} qualification
										{education.length > 1 ? 's' : ''}
									</Badge>
								)}
							</CardTitle>
							<CardDescription>
								Your educational qualifications. Click edit to request changes.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('education')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					{education && education.length > 0 ? (
						<div className="space-y-4">
							{education.map((edu, index) => (
								<div key={index} className="p-4 border rounded-lg bg-slate-50">
									<div className="flex justify-between items-start mb-2">
										<div>
											<h4 className="font-medium">{edu.degree}</h4>
											<p className="text-sm text-muted-foreground">
												{edu.institution}
											</p>
										</div>
										<Badge variant="outline">
											{edu.yearOfCompletion || 'N/A'}
										</Badge>
									</div>
									<div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
										{edu.fieldOfStudy && (
											<div>
												<span className="font-medium">Field of Study:</span>{' '}
												{edu.fieldOfStudy}
											</div>
										)}
										{edu.grade && (
											<div>
												<span className="font-medium">Grade/CGPA:</span>{' '}
												{edu.grade}
											</div>
										)}
									</div>
								</div>
							))}
						</div>
					) : (
						<div className="text-center py-8 text-muted-foreground">
							<GraduationCap className="h-12 w-12 mx-auto mb-4 opacity-50" />
							<p>No education information available</p>
							<p className="text-sm">
								Click &quot;Edit Section&quot; to add your qualifications
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
