'use client';

import { MainSidebar } from '@/components/main-sidebar';
import ProtectedLayout from '@/components/protected-layout';
import { ScrollArea } from '@/components/ui/scroll-area';
import { userRoles } from '@/lib/utils';

export default function ClientAdminDashboardLayout({ children }) {
	return (
		<ProtectedLayout
			userType={[
				userRoles.BUSINESS_ADMIN,
				userRoles.DEPARTMENT_ADMIN,
				userRoles.EMPLOYEE,
				userRoles.MODULE_ADMIN,
			]}
		>
			<MainSidebar
				role={[
					userRoles.BUSINESS_ADMIN,
					userRoles.DEPARTMENT_ADMIN,
					userRoles.EMPLOYEE,
					userRoles.MODULE_ADMIN,
				]}
			>
				<ScrollArea className="h-full">{children}</ScrollArea>
			</MainSidebar>
		</ProtectedLayout>
	);
}
