import { useState, useRef, useMemo, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { onboardingFinalStepSchema } from '@/lib/schemas/onboardingSchema';
import { useVirtualizer } from '@tanstack/react-virtual';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Button } from './ui/button';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { cn } from '@/lib/utils';
import { Check, ChevronsUpDown } from 'lucide-react';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import {
	fetchFileFromBlobURL,
	onboardingStepOne,
	setSteps,
	updateCurrencyAndTime,
	updateForm,
} from '@/lib/features/client-admin/clientAdminSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import { fetchCurrencies } from '@/lib/features/location/locationSlice';

export function TimeAndCurrencyDetails() {
	const dispatch = useAppDispatch();
	const { currencyAndTimeDetails } = useAppSelector(
		(store) => store.clientAdmin
	);
	const { currencies } = useAppSelector((store) => store.location);

	const form = useForm({
		resolver: zodResolver(onboardingFinalStepSchema),
		defaultValues: {
			currency: currencyAndTimeDetails?.currency || '',
			timeFormat: '',
			dateFormat: '',
			branches: [],
		},
	});

	useEffect(() => {
		dispatch(fetchCurrencies());
	}, [dispatch]);

	useEffect(() => {
		const subscription = form.watch((value) => {
			dispatch(updateCurrencyAndTime(value));
		});
		return () => subscription.unsubscribe();
	}, [form.watch, dispatch, form]);

	useEffect(() => {
		if (currencyAndTimeDetails?.currency) {
			form.setValue('currency', currencyAndTimeDetails?.currency);
		}
	}, [currencyAndTimeDetails?.currency, form]);

	const onSubmit = async (data) => {
		console.log(`onSubmit - data:`, data);
		dispatch(setSteps(3));
		// dispatch(onboardingStepOne(formData));
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="grid gap-4 w-full min-h-72"
			>
				<section className="grid grid-cols-12 gap-2">
					<FormField
						control={form.control}
						name="currency"
						render={({ field }) => (
							<FormItem className=" col-span-8">
								<FormLabel>Currency</FormLabel>
								<Popover>
									<PopoverTrigger asChild>
										<FormControl>
											<Button
												variant="outline"
												role="combobox"
												className={cn(
													'w-full justify-between',
													!field.value && 'text-muted-foreground'
												)}
											>
												{field.value
													? currencies?.find(
															(currency) => currency.currency === field.value
														)?.currency
													: 'Select currency'}
												<ChevronsUpDown className="opacity-50" />
											</Button>
										</FormControl>
									</PopoverTrigger>
									<PopoverContent className="w-full p-0">
										<Command>
											<CommandInput
												placeholder="Search Currency..."
												className="h-9"
											/>
											<CommandList>
												<CommandEmpty>No Currency found.</CommandEmpty>
												<CommandGroup>
													{currencies?.map((currency, index) => (
														<CommandItem
															value={currency.currency}
															key={index + 5}
															onSelect={() => {
																form.setValue('currency', currency.currency);
															}}
														>
															{`${currency.currencySymbol} ${currency.currency} (${currency.currencyName})`}
															<Check
																className={cn(
																	'ml-auto',
																	currency.currency === field.value
																		? 'opacity-100'
																		: 'opacity-0'
																)}
															/>
														</CommandItem>
													))}
												</CommandGroup>
											</CommandList>
										</Command>
									</PopoverContent>
								</Popover>
								<FormDescription>Your Business Currency</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={form.control}
						name="timeFormat"
						render={({ field }) => (
							<FormItem className="col-span-4">
								<FormLabel>Time Format</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
									className="w-full"
								>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Select a Time format" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="12h">12 Hours</SelectItem>
										<SelectItem value="24h">24 Hours</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="dateFormat"
						render={({ field }) => (
							<FormItem className="col-span-12">
								<FormLabel>Date Format</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
									className="w-full"
								>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Select a Date format" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
										<SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
										<SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
				</section>
				<Button type="submit" className="mt-auto">
					Submit
				</Button>
			</form>
		</Form>
	);
}
