'use client';

import { useEffect, useState, useRef } from 'react';
import { io } from 'socket.io-client';
import { QRCodeStyling } from '@liquid-js/qr-code-styling';
import { Wifi, WifiOff, RotateCcw } from 'lucide-react';
import { useAppDispatch } from '../lib/hooks';
import { loginAfterQrScan } from '../lib/features/auth/authSlice';

// const socket = io('http://localhost:5000/auth', {
// 	withCredentials: true,
// }); // Initialize socket connection
const socket = io('https://tms-backend-muzr.onrender.com/auth', {
	withCredentials: true,
}); // Initialize socket connection

const qrCode = new QRCodeStyling({
	width: 160, // Reduced from default 200
	height: 160, // Reduced from default 200
	type: 'png',
	image:
		'https://res.cloudinary.com/dv8nbs25p/image/upload/v1744395029/HarpHr/cousgbm3q0vxbdisz8g7.png',
	dotsOptions: {
		color: '#225850',
		type: 'rounded',
		size: 8, // Reduced dot size
	},
	backgroundOptions: {
		color: '#fff',
		margin: 0.5, // Reduced margin
	},
	imageOptions: {
		crossOrigin: 'anonymous',
		margin: 0.5, // Reduced image margin
		imageSize: 0.4, // Reduced image size
	},
	cornersSquareOptions: {
		type: 'extra-rounded',
		color: '#fbb309',
	},
	cornersDotOptions: {
		type: 'dot',
		color: '#225850',
	},
});

const QrLogin = () => {
	const ref = useRef(null);
	const [sessionId, setSessionId] = useState('');
	const [connectionStatus, setConnectionStatus] = useState('connecting');
	const [lastUpdated, setLastUpdated] = useState(null);
	const dispatch = useAppDispatch();
	useEffect(() => {
		// Connection status handlers
		// QR update handler

		socket.on('connect', () => {
			console.log('Socket connected:', socket.id);
		});

		socket.on('disconnect', () => {
			console.warn('Socket disconnected');
		});

		socket.on('qr-session', (data) => {
			const qrUrl = data.qrSessionId;
			setSessionId(data.qrSessionId);
			setLastUpdated(new Date());

			qrCode.update({ data: qrUrl });

			// Clear previous QR code before appending new one
			if (ref.current) {
				ref.current.innerHTML = '';
				qrCode.append(ref.current);
			}
		});

		// Authentication success handler
		socket.on('login-successful', (data) => {
			console.log('Authentication successful:', data);
			dispatch(loginAfterQrScan(data));
			// Handle successful authentication (redirect, etc.)
		});

		return () => {
			socket.off('qr-update');
			socket.off('login-successful');
			socket.off('connect');
			socket.off('disconnect');
		};
	}, [dispatch]);

	const getStatusIcon = () => {
		switch (connectionStatus) {
			case 'connected':
				return <Wifi className="h-3 w-3 text-green-500" />;
			case 'connecting':
				return <RotateCcw className="h-3 w-3 text-yellow-500 animate-spin" />;
			case 'disconnected':
			case 'error':
				return <WifiOff className="h-3 w-3 text-red-500" />;
			default:
				return <WifiOff className="h-3 w-3 text-gray-400" />;
		}
	};

	const getStatusText = () => {
		switch (connectionStatus) {
			case 'connected':
				return 'Connected';
			case 'connecting':
				return 'Connecting...';
			case 'disconnected':
				return 'Disconnected';
			case 'error':
				return 'Connection Error';
			default:
				return 'Unknown';
		}
	};

	return (
		<div className="flex flex-col items-center space-y-3">
			<div
				ref={ref}
				className="flex items-center justify-center w-40 h-40 bg-white rounded-lg border border-gray-200 shadow-sm"
			/>

			{/* Connection Status */}
			<div className="flex items-center space-x-2 text-xs">
				{getStatusIcon()}
				<span
					className={`${
						connectionStatus === 'connected'
							? 'text-green-600'
							: connectionStatus === 'connecting'
								? 'text-yellow-600'
								: 'text-red-600'
					}`}
				>
					{getStatusText()}
				</span>
			</div>

			{/* Session Info */}
			{sessionId && (
				<div className="text-center space-y-1">
					<div className="text-xs text-gray-500">
						Session ID: {sessionId.slice(-8)}
					</div>
					{lastUpdated && (
						<div className="text-xs text-gray-500">
							Last updated: {lastUpdated.toLocaleTimeString()}
						</div>
					)}
				</div>
			)}
		</div>
	);
};

export default QrLogin;
