import { Button } from '@/components/ui/button';
import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import { format, addDays, subDays, isToday as isTodayFn } from 'date-fns';
import { Separator } from '@/components/ui/separator';

const CalendarCard = () => {
	const [currentDate, setCurrentDate] = useState(new Date());

	const handlePrev = () => setCurrentDate((prev) => subDays(prev, 1));
	const handleNext = () => setCurrentDate((prev) => addDays(prev, 1));

	const isToday = (date) => isTodayFn(date);

	// Dummy events data
	const events = [
		{ date: new Date(), title: 'Diwali' },
		{ date: addDays(new Date(), 1), title: 'Meeting with Client' },
		{ date: subDays(new Date(), 1), title: 'Team Outing' },
		{ date: addDays(new Date(), 3), title: 'Code Review' },
	];

	const getEvents = (date) => {
		// Filter events that match the current date
		return events.filter(
			(event) =>
				event.date.getDate() === date.getDate() &&
				event.date.getMonth() === date.getMonth() &&
				event.date.getFullYear() === date.getFullYear()
		);
	};

	const eventsForToday = getEvents(currentDate);

	return (
		<div className="min-w-[280px] max-w-full sm:max-w-auto shadow-sm rounded-xl p-3 space-y-3 text-sm min-h-[200px] sm:min-h-[100px]">
			<div className="flex items-center justify-between gap-4 min-h-[200px] sm:min-h-[100px]">
				<Button
					variant="outline"
					size="icon"
					className="h-14 w-14"
					onClick={handlePrev}
				>
					<ChevronLeftIcon />
				</Button>
				<div className="flex-1 text-center h-full rounded-xl">
					<div className="text-base font-semibold text-card-foreground">
						{isToday(currentDate)
							? 'Today'
							: format(currentDate, 'do MMM, yyyy')}
					</div>
					<div className="space-y-2">
						{eventsForToday.length > 0 ? (
							eventsForToday.map((event, index) => (
								<div
									key={index}
									className="h-[50px] flex items-center justify-center text-2xl font-bold"
								>
									{event.title}
								</div>
							))
						) : (
							<div className="text-card-foreground opacity-80 pt-3">
								No events today
							</div>
						)}
					</div>
				</div>

				<Button
					variant="outline"
					size="icon"
					className="h-14 w-14"
					onClick={handleNext}
				>
					<ChevronRightIcon />
				</Button>
			</div>
		</div>
	);
};

export default CalendarCard;
