'use client';

import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { Loading } from './loading-component';
import { showUser } from '@/lib/features/auth/authSlice';
import { getCompanyDetails } from '@/lib/features/company-details/companyDetailsSlice';
import { userRoles } from '@/lib/utils';

export default function ProtectedLayout({ children, userType }) {
	const dispatch = useAppDispatch();
	const router = useRouter();

	const { authenticatedUser, isLoading, isLoggedOut } = useAppSelector(
		(store) => store.auth
	);

	const [isChecking, setIsChecking] = useState(true);

	// Fetch user if not already present
	useEffect(() => {
		const fetchUser = async () => {
			if (!authenticatedUser) {
				await dispatch(showUser());
			}
			setIsChecking(false);
		};

		fetchUser();
	}, [dispatch, authenticatedUser]);

	// Fetch company details for client admins
	useEffect(() => {
		const role = authenticatedUser?.role;
		const onboarded = authenticatedUser?.isOnboard;

		if (
			role === userRoles.CLIENT_ADMIN ||
			(role === userRoles.GLORIFIED_CLIENT_ADMIN && onboarded) || 
			role > userRoles.CLIENT_ADMIN
		) {
			dispatch(getCompanyDetails());
		}
	}, [dispatch, authenticatedUser?.role, authenticatedUser?.isOnboard]);

	// Check if user has access
	const hasAccess = useMemo(() => {
		return authenticatedUser && userType.includes(authenticatedUser.role);
	}, [authenticatedUser, userType]);

	// Handle redirects and toasts
	useEffect(() => {
		if (isChecking || isLoading) return;

		if (!authenticatedUser) {
			if (!isLoggedOut) {
				// toast.warning('Please log in to access this page.');
				router.push('/login');
			}
		} else if (!hasAccess) {
			toast.error("You don't have permission to access this page.");
			router.push('/login');
		}
	}, [
		authenticatedUser,
		hasAccess,
		isChecking,
		isLoading,
		isLoggedOut,
		router,
	]);

	if (isChecking || isLoading) {
		return <Loading isLoading={isLoading} />;
	}

	// Optional: Replace `null` with an <AccessDenied /> component for better UX
	// return hasAccess ? children : null;
	return hasAccess ? children : <AccessDenied />;
}
const AccessDenied = () => (
	<div className="text-center mt-10 text-red-600 text-xl font-semibold">
		Access Denied. You do not have permission to view this page.
	</div>
);

// Then change return:
