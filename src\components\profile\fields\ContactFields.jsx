import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { MultipleEntriesManager } from '../MultipleEntriesManager';

export function ContactFields({ form }) {
	const contacts = form.watch('contacts') || [];

	const handleContactsChange = (newContacts) => {
		form.setValue('contacts', newContacts);
	};

	return (
		<>
			<MultipleEntriesManager
				section="contact"
				entries={contacts}
				onEntriesChange={handleContactsChange}
			/>

			<FormField
				control={form.control}
				name="reason"
				render={({ field }) => (
					<FormItem>
						<FormLabel>Reason for Change *</FormLabel>
						<FormControl>
							<Textarea
								placeholder="Please provide a reason for these changes..."
								className="min-h-[100px]"
								{...field}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</>
	);
}
